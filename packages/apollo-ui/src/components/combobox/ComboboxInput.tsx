import { HTMLProps } from "react"
import { Combobox as BaseCombobox } from "@base-ui-components/react/combobox"
import classNames from "classnames"

import { Chip } from "../chip"
import { ChevronIcon, CloseIcon } from "../common"
import styles from "./combobox-input.module.css"
import type { ComboboxOption, ComboboxValueType } from "./ComboboxProps"

export type ComboboxInputProps<
  ItemValue extends ComboboxOption,
  Multiple extends boolean | undefined = false,
> = {
  id: string
  multiple?: Multiple
  disabled?: boolean
  placeholder?: string
  size?: "small" | "medium"
  error?: boolean
  options?: ItemValue[]
  itemToStringLabel?: (itemId: string) => string
  onValueChange?: (value: ComboboxValueType<ItemValue, Multiple> | null) => void
  slotProps?: {
    chips?: {
      className?: string
    }
    input?: {
      className?: string
      render?: (
        props: HTMLProps<HTMLInputElement>,
        state: BaseCombobox.Input.State
      ) => React.ReactNode
    }
    chip?: {
      className?: string
      render?: (
        props: HTMLProps<HTMLDivElement> & { optionData: ComboboxOption },
        state: BaseCombobox.Chip.State
      ) => React.ReactNode
    }
  }
}

export function ComboboxInput<
  ItemValue extends ComboboxOption,
  Multiple extends boolean | undefined = false,
>({
  id,
  multiple,
  disabled,
  placeholder,
  size = "medium",
  error,
  options,
  itemToStringLabel,
  onValueChange,
  slotProps,
}: ComboboxInputProps<ItemValue, Multiple>) {
  return (
    <BaseCombobox.Chips
      className={classNames(
        "ApolloCombobox-chips",
        styles.comboboxChips,
        {
          [styles.comboboxChipsSmall]: size === "small",
          [styles.comboboxChipsMedium]: size === "medium",
          [styles.comboboxChipsError]: error,
          [styles.comboboxChipsDisabled]: disabled,
        },
        slotProps?.chips?.className
      )}
    >
      <BaseCombobox.Value>
        {(currentValue) => {
          return (
            <>
              {multiple &&
                Array.isArray(currentValue) &&
                currentValue.length > 0 &&
                currentValue.map((value) => (
                  <BaseCombobox.Chip
                    key={"chip-" + value}
                    render={(
                      props: HTMLProps<HTMLDivElement>,
                      state: BaseCombobox.Chip.State
                    ) => {
                      if (slotProps?.chip?.render) {
                        return slotProps.chip.render(
                          {
                            optionData:
                              options?.find(
                                (option) => option.id === value
                              ) ?? value,
                            ...props,
                          },
                          state
                        ) as React.ReactElement
                      }
                      return (
                        <Chip
                          label={
                            options?.find((option) => option.id === value)
                              ?.label || value
                          }
                          size={size === "small" ? "small" : "medium"}
                          disabled={state.disabled || disabled}
                          onClose={(event) => {
                            event.stopPropagation()
                            // Filter out the removed option
                            const remainingOptions = currentValue.filter(
                              (selectedValue) => selectedValue !== value
                            )
                            onValueChange?.(
                              remainingOptions as ComboboxValueType<
                                ItemValue,
                                Multiple
                              >
                            )
                          }}
                          className={classNames(
                            "ApolloCombobox-chip",
                            slotProps?.chip?.className
                          )}
                        />
                      )
                    }}
                  />
                ))}
              <BaseCombobox.Input
                id={id}
                render={(
                  inputProps: HTMLProps<HTMLInputElement>,
                  inputState: BaseCombobox.Input.State
                ) => {
                  if (slotProps?.input?.render) {
                    const customRender = slotProps.input.render(
                      inputProps,
                      inputState
                    )
                    if (customRender) {
                      return customRender as React.ReactElement
                    }
                  }

                  const hasValue = multiple
                    ? Array.isArray(currentValue) && currentValue.length > 0
                    : Boolean(currentValue)
                  const hasSearchInput = Boolean(inputProps.value)
                  const showClear = hasValue || hasSearchInput

                  // Determine the input value to display
                  let inputValue = inputProps.value || ""

                  if (!multiple && currentValue && !inputProps.value) {
                    // In single-select mode, show the selected item's label when not searching
                    if (itemToStringLabel) {
                      inputValue = itemToStringLabel(currentValue as string)
                    } else {
                      const selectedOption = options?.find(
                        (option) => option.id === currentValue
                      )
                      inputValue = selectedOption?.label || (currentValue as string)
                    }
                  } else if (multiple && hasValue) {
                    // In multiple mode with selections, clear the input to show chips
                    inputValue = inputProps.value || ""
                  }

                  return (
                    <div
                      className={classNames(
                        "ApolloCombobox-inputWrapper",
                        styles.comboboxInputWrapper
                      )}
                    >
                      <input
                        {...inputProps}
                        value={inputValue}
                        className={classNames(
                          "ApolloCombobox-input",
                          styles.comboboxInputElement,
                          {
                            [styles.comboboxInputSmall]: size === "small",
                          },
                          slotProps?.input?.className
                        )}
                        disabled={disabled || inputState.disabled}
                        placeholder={placeholder}
                      />
                      <div
                        className={classNames(
                          "ApolloCombobox-endDecorator",
                          styles.comboboxInputEndDecorator
                        )}
                      >
                        {showClear && (
                          <BaseCombobox.Clear
                            className={styles.comboboxClear}
                            aria-label="Clear selection"
                            disabled={disabled}
                          >
                            <CloseIcon />
                          </BaseCombobox.Clear>
                        )}
                        <BaseCombobox.Trigger
                          className={styles.comboboxTrigger}
                          aria-label="Open popup"
                          disabled={disabled}
                        >
                          <ChevronIcon />
                        </BaseCombobox.Trigger>
                      </div>
                    </div>
                  )
                }}
              />
            </>
          )
        }}
      </BaseCombobox.Value>
    </BaseCombobox.Chips>
  )
}
