import { HTMLProps } from "react"
import { Combobox as BaseCombobox } from "@base-ui-components/react/combobox"
import classNames from "classnames"

import { Chip } from "../../chip"
import { ChevronIcon, CloseIcon } from "../../common"
import styles from "./combobox.module.css"
import type { ComboboxOption } from "../ComboboxProps"

export type MultipleInputProps<ItemValue extends ComboboxOption> = {
  id: string
  disabled?: boolean
  placeholder?: string
  size?: "small" | "medium"
  error?: boolean
  currentValue: string[] | null
  options?: ItemValue[]
  onValueChange?: (value: ItemValue[] | null) => void
  slotProps?: {
    chips?: {
      className?: string
    }
    input?: {
      className?: string
      render?: (
        props: HTMLProps<HTMLInputElement>,
        state: BaseCombobox.Input.State
      ) => React.ReactNode
    }
    chip?: {
      className?: string
      render?: (
        props: HTMLProps<HTMLDivElement> & { optionData: ComboboxOption },
        state: BaseCombobox.Chip.State
      ) => React.ReactNode
    }
  }
}

export function MultipleInput<ItemValue extends ComboboxOption>({
  id,
  disabled,
  placeholder,
  size = "medium",
  error,
  currentValue,
  options,
  onValueChange,
  slotProps,
}: MultipleInputProps<ItemValue>) {
  return (
    <BaseCombobox.Chips
      className={classNames(
        "ApolloCombobox-chips",
        styles.comboboxChips,
        {
          [styles.comboboxChipsSmall]: size === "small",
          [styles.comboboxChipsMedium]: size === "medium",
          [styles.comboboxChipsError]: error,
          [styles.comboboxChipsDisabled]: disabled,
        },
        slotProps?.chips?.className
      )}
    >
      {Array.isArray(currentValue) &&
        currentValue.length > 0 &&
        currentValue.map((value) => (
          <BaseCombobox.Chip
            key={"chip-" + value}
            render={(
              props: HTMLProps<HTMLDivElement>,
              state: BaseCombobox.Chip.State
            ) => {
              if (slotProps?.chip?.render) {
                const optionData =
                  options?.find((option) => option.id === value) ?? ({
                    id: value,
                    label: value,
                  } as ComboboxOption)
                return slotProps.chip.render(
                  {
                    optionData,
                    ...props,
                  },
                  state
                ) as React.ReactElement
              }
              return (
                <Chip
                  label={
                    options?.find((option) => option.id === value)?.label ||
                    value
                  }
                  size={size === "small" ? "small" : "medium"}
                  disabled={state.disabled || disabled}
                  onClose={(event) => {
                    event.stopPropagation()
                    // Filter out the removed option
                    const remainingOptions = currentValue.filter(
                      (selectedValue) => selectedValue !== value
                    )
                    onValueChange?.(remainingOptions as unknown as ItemValue[])
                  }}
                  className={classNames(
                    "ApolloCombobox-chip",
                    slotProps?.chip?.className
                  )}
                />
              )
            }}
          />
        ))}
      <BaseCombobox.Input
        id={id}
        render={(
          inputProps: HTMLProps<HTMLInputElement>,
          inputState: BaseCombobox.Input.State
        ) => {
          if (slotProps?.input?.render) {
            const customRender = slotProps.input.render(inputProps, inputState)
            if (customRender) {
              return customRender as React.ReactElement
            }
          }

          const hasValue =
            Array.isArray(currentValue) && currentValue.length > 0
          const hasSearchInput = Boolean(inputProps.value)
          const showClear = hasValue || hasSearchInput

          // In multiple mode with selections, clear the input to show chips
          const inputValue = inputProps.value || ""

          return (
            <div
              className={classNames(
                "ApolloCombobox-inputWrapper",
                styles.comboboxInputWrapper
              )}
            >
              <input
                {...inputProps}
                value={inputValue}
                className={classNames(
                  "ApolloCombobox-input",
                  styles.comboboxInputElement,
                  {
                    [styles.comboboxInputSmall]: size === "small",
                  },
                  slotProps?.input?.className
                )}
                disabled={disabled || inputState.disabled}
                placeholder={placeholder}
              />
              <div
                className={classNames(
                  "ApolloCombobox-endDecorator",
                  styles.comboboxInputEndDecorator
                )}
              >
                {showClear && (
                  <BaseCombobox.Clear
                    className={styles.comboboxClear}
                    aria-label="Clear selection"
                    disabled={disabled}
                  >
                    <CloseIcon />
                  </BaseCombobox.Clear>
                )}
                <BaseCombobox.Trigger
                  className={styles.comboboxTrigger}
                  aria-label="Open popup"
                  disabled={disabled}
                >
                  <ChevronIcon />
                </BaseCombobox.Trigger>
              </div>
            </div>
          )
        }}
      />
    </BaseCombobox.Chips>
  )
}

