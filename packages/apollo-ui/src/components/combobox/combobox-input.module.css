@layer legacy {
  .comboboxChips {
    --apl-input-border-radius: 8px;
    --apl-input-border: var(--apl-colors-border-default, #D3D7E1);
    --apl-input-background: var(--apl-colors-surface-static-ui-default, #FFF);
    --apl-input-text: var(--apl-colors-content-default, #0C0E11);
    --apl-input-placeholder: var(--apl-colors-content-placeholder, #BEC4D1);
    --apl-input-gap: var(--apl-space-gap-xs, 8px);
    --apl-input-hover-border: var(--apl-colors-border-primary-subdued, #409261);
    --apl-input-focus-border: var(--apl-colors-border-primary-default, #006D2E);
    --apl-input-padding-lr: var(--apl-space-padding-xs, 8px);
    --apl-input-small-padding-tb: var(--apl-space-padding-2xs, 4px);
    --apl-input-medium-padding-tb: var(--apl-space-padding-xs, 8px);
    --apl-input-invalid-border: var(--apl-colors-border-danger-default, #E74747);
    --apl-input-disabled-border: var(--apl-colors-border-disabled, #BEC4D1);
    --apl-input-disabled-background: var(--apl-colors-surface-static-ui-disabled, #F6F7FB);
    --apl-input-disabled-text-color: var(--apl-colors-content-description, #5C6372);
    --apl-input-small-font-size: var(--apl-typography-body2-font-size, 14px);
    --apl-input-medium-font-size: var(--apl-typography-body1-font-size, 16px);
  }
}

@layer apollo {
  .comboboxChips {
    --apl-input-border-radius: var(--apl-alias-radius-radius4, 8px);
    --apl-input-border: var(--apl-alias-color-outline-and-border-outline, #ADABAB);
    --apl-input-background: var(--apl-alias-color-background-and-surface-background, #FFF);
    --apl-input-text: var(--apl-alias-color-background-and-surface-on-surface, #474647);
    --apl-input-placeholder: var(--apl-alias-color-background-and-surface-on-surface-variant, #C8C6C6);
    --apl-input-gap: var(--apl-alias-spacing-gap-gap4, 6px);
    --apl-input-hover-border: var(--apl-alias-color-primary-hovered, #2C8745);
    --apl-input-focus-border: var(--apl-alias-color-primary-focused, #49A25C);
    --apl-input-padding-lr: var(--apl-alias-spacing-padding-padding5, 8px);
    --apl-input-small-padding-tb: var(--apl-alias-spacing-padding-padding3, 4px);
    --apl-input-medium-padding-tb: var(--apl-alias-spacing-padding-padding4, 6px);
    --apl-input-invalid-border: var(--apl-alias-color-error-error, #C0000B);
    --apl-input-disabled-border: var(--apl-alias-color-outline-and-border-outline, #ADABAB);
    --apl-input-disabled-background: var(--apl-alias-color-background-and-surface-surface, #F8F7F7);
    --apl-input-disabled-text-color: var(--apl-alias-color-background-and-surface-on-surface, #474647);
    --apl-input-small-font-size: var(--apl-alias-typography-body-large-font-size, 16px);
    --apl-input-medium-font-size: var(--apl-alias-typography-body-large-font-size, 16px);
  }
}

.comboboxChips {
  box-sizing: border-box;
  border-radius: var(--apl-input-border-radius);
  border: 1px solid var(--apl-input-border);
  background: var(--apl-input-background);
  color: var(--apl-input-text);

  min-height: var(--apl-input-height);

  /* Shadow text input - same as Input component */
  box-shadow: 1px 1px 0px 0px rgba(154, 154, 154, 0.08);

  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--apl-input-gap);
  align-self: stretch;
  outline: none;
  width: 100%;

  padding: 0px var(--apl-input-padding-lr);

  transition: background 150ms cubic-bezier(0.4, 0, 0.2, 1);

  &:hover:not(.comboboxChipsError):not(.comboboxChipsDisabled) {
    border-color: var(--apl-input-hover-border);
  }

  &:focus-within:not(.comboboxChipsError):not(.comboboxChipsDisabled) {
    border-color: var(--apl-input-focus-border);
  }
}

.comboboxChipsError {
  border-color: var(--apl-input-invalid-border);
  background: var(--apl-input-background);
  box-shadow: 1px 1px 0px 0px rgba(154, 154, 154, 0.08);
}

.comboboxChipsDisabled {
  border-color: var(--apl-input-disabled-border);
  background: var(--apl-input-disabled-background);
  color: var(--apl-input-disabled-text-color);
}

.comboboxChipsSmall {
  --apl-input-height: 32px;
}

.comboboxChipsMedium {
  --apl-input-height: 42px;
}

.comboboxInputWrapper {
  flex: 1 1 auto;
  display: flex;
  align-items: center;
  gap: var(--apl-input-gap);
  min-width: 120px;
}

.comboboxInputElement {
  flex: 1;
  min-width: 0;
  border: none;
  outline: none;
  background: transparent;
  width: 100%;
  align-self: stretch;

  composes: apl-typography-body-large from '../../base.module.css';
  font-size: var(--apl-input-medium-font-size);

  &::placeholder {
    color: var(--apl-input-placeholder);
  }
}

.comboboxInputSmall {
  composes: apl-typography-body-large from '../../base.module.css';
  font-size: var(--apl-input-small-font-size);
}

.comboboxInputEndDecorator {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.comboboxClear,
.comboboxTrigger {
  color: var(--apl-alias-color-background-and-surface-on-surface, #474647);

  &[data-disabled] {
    color: var(--apl-alias-color-background-and-surface-text-icon-disabled, #ADABAB);
  }
}

/* Hide Clear button when Base UI marks it hidden */
.comboboxClear[hidden],
.comboboxClear[data-hidden] {
  display: none !important;
}
