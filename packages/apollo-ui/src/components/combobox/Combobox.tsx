import { HTMLProps, useCallback, useId, useMemo, useRef } from "react"
import { Combobox as BaseCombobox } from "@base-ui-components/react/combobox"
import classNames from "classnames"

import { ChevronIcon, CloseIcon } from "../common"
import { Field } from "../field"
import { Input } from "../input"
import { MenuItem } from "../menu-item"
import { Portal } from "../portal"
import styles from "./combobox.module.css"
import type {
  ComboboxOption,
  ComboboxProps,
  ComboboxValueType,
} from "./ComboboxProps"
import { MultipleInput } from "./multiple/MultipleInput"
import { SingleInput } from "./single/SingleInput"

export function Combobox<
  ItemValue extends ComboboxOption,
  Multiple extends boolean | undefined = false,
>({
  label,
  fullWidth,
  required,
  helperText,
  error,
  disabled,
  placeholder,
  options,
  ref,
  onValueChange,
  labelDecorator,
  helperTextDecorator,
  size = "medium",
  slotProps,
  multiple,
  itemToStringLabel,
  ...baseRootProps
}: ComboboxProps<ItemValue, Multiple>) {
  const id = useId()
  const internalRef = useRef<HTMLDivElement | null>(null)
  const containerRef = useMemo(() => ref ?? internalRef, [ref])

  const handleValueChange = useCallback(
    (value: ComboboxValueType<ItemValue, Multiple> | null) => {
      onValueChange?.(value)
    },
    [onValueChange]
  )

  // const

  return (
    <BaseCombobox.Root
      {...baseRootProps}
      multiple={multiple}
      items={options}
      onValueChange={handleValueChange}
      itemToStringLabel={(item) => {
        // If a custom itemToStringLabel is provided, use it with the item's id
        if (itemToStringLabel) {
          return itemToStringLabel(item.id)
        }
        // Otherwise, find the option and return its label, fallback to the id
        return (
          options?.find((option) => option.id === item.id)?.label ?? item.id
        )
      }}
    >
      <div
        className={classNames(
          "ApolloCombobox-container",
          styles.comboboxRoot,
          slotProps?.container?.className
        )}
        ref={containerRef}
      >
        <Field
          label={label}
          required={required}
          helperText={helperText}
          error={error}
          labelDecorator={labelDecorator}
          helperTextDecorator={helperTextDecorator}
          className={classNames("ApolloCombobox-field", {
            [styles.comboboxFieldFullWidth]: fullWidth,
          })}
        >
          {multiple ? (
            <BaseCombobox.Value>
              {(currentValue) => (
                <MultipleInput
                  id={id}
                  disabled={disabled}
                  placeholder={placeholder}
                  size={size}
                  error={error}
                  currentValue={currentValue as string[] | null}
                  options={options}
                  onValueChange={
                    onValueChange as (value: ItemValue[] | null) => void
                  }
                  slotProps={slotProps}
                />
              )}
            </BaseCombobox.Value>
          ) : (
            <BaseCombobox.Value>
              {(currentValue) => (
                <Input
                  id={id}
                  // {...inputProps}
                  value={currentValue}
                  // label={label}
                  // required={required}
                  // helperText={helperText}
                  // error={error}
                  disabled={disabled}
                  placeholder={placeholder}
                  fullWidth={fullWidth}
                  size={size}
                  // labelDecorator={labelDecorator}
                  // helperTextDecorator={helperTextDecorator}
                  className={classNames(
                    "ApolloCombobox-input",
                    slotProps?.input?.className
                  )}
                  // endDecorator={
                  //   <div
                  //     className={classNames(
                  //       "ApolloCombobox-endDecorator",
                  //       styles.comboboxInputEndDecorator
                  //     )}
                  //   >
                  //     {inputProps.value && (
                  //       <BaseCombobox.Clear
                  //         className={styles.comboboxClear}
                  //         aria-label="Clear selection"
                  //         disabled={disabled}
                  //       >
                  //         <CloseIcon />
                  //       </BaseCombobox.Clear>
                  //     )}
                  //     <BaseCombobox.Trigger
                  //       className={styles.comboboxTrigger}
                  //       aria-label="Open popup"
                  //       disabled={disabled}
                  //     >
                  //       <ChevronIcon />
                  //     </BaseCombobox.Trigger>
                  //   </div>
                  // }
                />
              )}
            </BaseCombobox.Value>
            // <BaseCombobox.Input
            //   // id={id}
            //   render={(
            //     inputProps: HTMLProps<HTMLInputElement>,
            //     inputState: BaseCombobox.Input.State
            //   ) => {
            //     if (slotProps?.input?.render) {
            //       const customRender = slotProps.input.render(
            //         inputProps,
            //         inputState
            //       )
            //       if (customRender) {
            //         return customRender as React.ReactElement
            //       }
            //     }

            //     return (
            //       // <BaseCombobox.Value>
            //       //   {(currentValue) => (
            //           <Input
            //             {...inputProps}
            //             // value={currentValue}
            //             label={label}
            //             required={required}
            //             helperText={helperText}
            //             error={error}
            //             disabled={disabled || inputState.disabled}
            //             placeholder={placeholder}
            //             fullWidth={fullWidth}
            //             size={size}
            //             labelDecorator={labelDecorator}
            //             helperTextDecorator={helperTextDecorator}
            //             className={classNames(
            //               "ApolloCombobox-input",
            //               slotProps?.input?.className
            //             )}
            //             endDecorator={
            //               <div
            //                 className={classNames(
            //                   "ApolloCombobox-endDecorator",
            //                   styles.comboboxInputEndDecorator
            //                 )}
            //               >
            //                 {inputProps.value && (
            //                   <BaseCombobox.Clear
            //                     className={styles.comboboxClear}
            //                     aria-label="Clear selection"
            //                     disabled={disabled}
            //                   >
            //                     <CloseIcon />
            //                   </BaseCombobox.Clear>
            //                 )}
            //                 <BaseCombobox.Trigger
            //                   className={styles.comboboxTrigger}
            //                   aria-label="Open popup"
            //                   disabled={disabled}
            //                 >
            //                   <ChevronIcon />
            //                 </BaseCombobox.Trigger>
            //               </div>
            //             }
            //           />
            //         // )}
            //       // </BaseCombobox.Value>
            //     )
            //   }}
            // />
          )}
        </Field>
      </div>
      <Portal baseComponent={<BaseCombobox.Portal />}>
        <BaseCombobox.Positioner
          positionMethod="fixed"
          sticky
          sideOffset={4}
          anchor={containerRef}
          className={classNames(
            "ApolloCombobox-positioner",
            styles.comboboxMenuPositioner,
            slotProps?.positioner?.className
          )}
        >
          <BaseCombobox.Popup
            className={classNames(
              "ApolloCombobox-popup",
              styles.Popup,
              slotProps?.popup?.className
            )}
          >
            <BaseCombobox.List>
              {(item: ItemValue) => {
                return (
                  <BaseCombobox.Item
                    key={"item-" + item.id}
                    value={item.id}
                    disabled={item?.disabled}
                    className={classNames(
                      "ApolloCombobox-option",
                      styles.comboboxMenuItem
                    )}
                    render={(
                      props: HTMLProps<HTMLDivElement>,
                      state: BaseCombobox.Item.State
                    ) => {
                      if (slotProps?.option?.render) {
                        return slotProps.option.render(
                          { optionData: item, ...props },
                          state
                        ) as React.ReactElement
                      }
                      if (item.renderLabel) {
                        return item.renderLabel() as React.ReactElement
                      }
                      return (
                        <MenuItem
                          label={item.label}
                          {...props}
                          className={classNames(
                            "ApolloCombobox-menuItem",
                            props?.className
                          )}
                          selected={state.selected}
                          disabled={state.disabled}
                        />
                      )
                    }}
                  />
                )
              }}
            </BaseCombobox.List>
          </BaseCombobox.Popup>
        </BaseCombobox.Positioner>
      </Portal>
    </BaseCombobox.Root>
  )
}
