import { HTMLProps, useCallback, useId, useMemo, useRef } from "react"
import { Combobox as BaseCombobox } from "@base-ui-components/react/combobox"
import classNames from "classnames"

import { Chip } from "../chip"
import { ChevronIcon, CloseIcon } from "../common"
import { Field } from "../field"
import { MenuItem } from "../menu-item"
import { Portal } from "../portal"
import styles from "./combobox.module.css"
import type {
  ComboboxOption,
  ComboboxProps,
  ComboboxValueType,
} from "./ComboboxProps"

export function Combobox<
  ItemValue extends ComboboxOption,
  Multiple extends boolean | undefined = false,
>({
  label,
  fullWidth,
  required,
  helperText,
  error,
  disabled,
  placeholder,
  options,
  ref,
  onValueChange,
  labelDecorator,
  helperTextDecorator,
  size = "medium",
  slotProps,
  multiple,
  itemToStringLabel,
  ...baseRootProps
}: ComboboxProps<ItemValue, Multiple>) {
  const id = useId()
  const internalRef = useRef<HTMLDivElement | null>(null)
  const containerRef = useMemo(() => ref ?? internalRef, [ref])

  const handleValueChange = useCallback(
    (value: ComboboxValueType<ItemValue, Multiple> | null) => {
      onValueChange?.(value)
    },
    [onValueChange]
  )

  // const

  return (
    <BaseCombobox.Root
      {...baseRootProps}
      multiple={multiple}
      items={options}
      onValueChange={handleValueChange}
      itemToStringLabel={(item) => {
        // If a custom itemToStringLabel is provided, use it with the item's id
        if (itemToStringLabel) {
          return itemToStringLabel(item.id)
        }
        // Otherwise, find the option and return its label, fallback to the id
        return (
          options?.find((option) => option.id === item.id)?.label ?? item.id
        )
      }}
    >
      <Field
        label={label}
        required={required}
        helperText={helperText}
        error={error}
        labelDecorator={labelDecorator}
        helperTextDecorator={helperTextDecorator}
        className={classNames("ApolloCombobox-field", {
          [styles.comboboxFieldFullWidth]: fullWidth,
        })}
      >
        <div
          className={classNames(
            "ApolloCombobox-container",
            styles.comboboxRoot,
            slotProps?.container?.className
          )}
          ref={containerRef}
        >
          <BaseCombobox.Chips
            className={classNames(
              "ApolloCombobox-chips",
              styles.comboboxChips,
              {
                [styles.comboboxChipsSmall]: size === "small",
                [styles.comboboxChipsMedium]: size === "medium",
                [styles.comboboxChipsError]: error,
                [styles.comboboxChipsDisabled]: disabled,
              },
              slotProps?.chips?.className
            )}
          >
          <BaseCombobox.Value>
            {(currentValue) => {
              return (
                <>
                  {multiple &&
                    Array.isArray(currentValue) &&
                    currentValue.length > 0 &&
                    currentValue.map((value) => (
                      <BaseCombobox.Chip
                        key={"chip-" + value}
                        render={(
                          props: HTMLProps<HTMLDivElement>,
                          state: BaseCombobox.Chip.State
                        ) => {
                          if (slotProps?.chip?.render) {
                            return slotProps.chip.render(
                              {
                                optionData:
                                  options?.find(
                                    (option) => option.id === value
                                  ) ?? value,
                                ...props,
                              },
                              state
                            ) as React.ReactElement
                          }
                          return (
                            <Chip
                              label={
                                options?.find((option) => option.id === value)
                                  ?.label || value
                              }
                              size={size === "small" ? "small" : "medium"}
                              disabled={state.disabled || disabled}
                              onClose={(event) => {
                                event.stopPropagation()
                                // Filter out the removed option
                                const remainingOptions = currentValue.filter(
                                  (selectedValue) => selectedValue !== value
                                )
                                onValueChange?.(
                                  remainingOptions as ComboboxValueType<
                                    ItemValue,
                                    Multiple
                                  >
                                )
                              }}
                              className={classNames(
                                "ApolloCombobox-chip",
                                slotProps?.chip?.className
                              )}
                            />
                          )
                        }}
                      />
                    ))}
                  <BaseCombobox.Input
                    id={id}
                    render={(
                      inputProps: HTMLProps<HTMLInputElement>,
                      inputState: BaseCombobox.Input.State
                    ) => {
                      if (slotProps?.input?.render) {
                        const customRender = slotProps.input.render(
                          inputProps,
                          inputState
                        )
                        if (customRender) {
                          return customRender as React.ReactElement
                        }
                      }

                      const hasValue = multiple
                        ? Array.isArray(currentValue) && currentValue.length > 0
                        : Boolean(currentValue)
                      const hasSearchInput = Boolean(inputProps.value)
                      const showClear = hasValue || hasSearchInput

                      // Determine the input value to display
                      let inputValue = inputProps.value || ""

                      if (!multiple && currentValue && !inputProps.value) {
                        // In single-select mode, show the selected item's label when not searching
                        if (itemToStringLabel) {
                          inputValue = itemToStringLabel(currentValue as string)
                        } else {
                          const selectedOption = options?.find(
                            (option) => option.id === currentValue
                          )
                          inputValue = selectedOption?.label || (currentValue as string)
                        }
                      } else if (multiple && hasValue) {
                        // In multiple mode with selections, clear the input to show chips
                        inputValue = inputProps.value || ""
                      }

                      return (
                        <div
                          className={classNames(
                            "ApolloCombobox-inputWrapper",
                            styles.comboboxInputWrapper
                          )}
                        >
                          <input
                            {...inputProps}
                            value={inputValue}
                            className={classNames(
                              "ApolloCombobox-input",
                              styles.comboboxInputElement,
                              {
                                [styles.comboboxInputSmall]: size === "small",
                              },
                              slotProps?.input?.className
                            )}
                            disabled={disabled || inputState.disabled}
                            placeholder={placeholder}
                          />
                          <div
                            className={classNames(
                              "ApolloCombobox-endDecorator",
                              styles.comboboxInputEndDecorator
                            )}
                          >
                            {showClear && (
                              <BaseCombobox.Clear
                                className={styles.comboboxClear}
                                aria-label="Clear selection"
                                disabled={disabled}
                              >
                                <CloseIcon />
                              </BaseCombobox.Clear>
                            )}
                            <BaseCombobox.Trigger
                              className={styles.comboboxTrigger}
                              aria-label="Open popup"
                              disabled={disabled}
                            >
                              <ChevronIcon />
                            </BaseCombobox.Trigger>
                          </div>
                        </div>
                      )
                    }}
                  />
                </>
              )
            }}
          </BaseCombobox.Value>
        </BaseCombobox.Chips>
      </div>
      </Field>
      <Portal baseComponent={<BaseCombobox.Portal />}>
        <BaseCombobox.Positioner
          positionMethod="fixed"
          sticky
          sideOffset={4}
          anchor={containerRef}
          className={classNames(
            "ApolloCombobox-positioner",
            styles.comboboxMenuPositioner,
            slotProps?.positioner?.className
          )}
        >
          <BaseCombobox.Popup
            className={classNames(
              "ApolloCombobox-popup",
              styles.Popup,
              slotProps?.popup?.className
            )}
          >
            <BaseCombobox.List>
              {(item: ItemValue) => {
                return (
                  <BaseCombobox.Item
                    key={"item-" + item.id}
                    value={item.id}
                    disabled={item?.disabled}
                    className={classNames(
                      "ApolloCombobox-option",
                      styles.comboboxMenuItem
                    )}
                    render={(
                      props: HTMLProps<HTMLDivElement>,
                      state: BaseCombobox.Item.State
                    ) => {
                      if (slotProps?.option?.render) {
                        return slotProps.option.render(
                          { optionData: item, ...props },
                          state
                        ) as React.ReactElement
                      }
                      if (item.renderLabel) {
                        return item.renderLabel() as React.ReactElement
                      }
                      return (
                        <MenuItem
                          label={item.label}
                          {...props}
                          className={classNames(
                            "ApolloCombobox-menuItem",
                            props?.className
                          )}
                          selected={state.selected}
                          disabled={state.disabled}
                        />
                      )
                    }}
                  />
                )
              }}
            </BaseCombobox.List>
          </BaseCombobox.Popup>
        </BaseCombobox.Positioner>
      </Portal>
    </BaseCombobox.Root>
  )
}
