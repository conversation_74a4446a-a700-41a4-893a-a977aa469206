import { HTMLProps } from "react"
import { Combobox as BaseCombobox } from "@base-ui-components/react/combobox"
import classNames from "classnames"

import { ChevronIcon, CloseIcon } from "../common"
import { Input } from "../input"
import styles from "./combobox.module.css"
import type { ComboboxOption } from "./ComboboxProps"

export type SingleInputProps<ItemValue extends ComboboxOption> = {
  id: string
  label?: React.ReactNode
  required?: boolean
  helperText?: React.ReactNode
  error?: boolean
  disabled?: boolean
  placeholder?: string
  fullWidth?: boolean
  size?: "small" | "medium"
  labelDecorator?: React.ReactNode
  helperTextDecorator?: React.ReactNode
  currentValue: string | null
  options?: ItemValue[]
  itemToStringLabel?: (itemId: string) => string
  slotProps?: {
    input?: {
      className?: string
      render?: (
        props: HTMLProps<HTMLInputElement>,
        state: BaseCombobox.Input.State
      ) => React.ReactNode
    }
  }
}

export function SingleInput<ItemValue extends ComboboxOption>({
  id,
  label,
  required,
  helperText,
  error,
  disabled,
  placeholder,
  fullWidth,
  size = "medium",
  labelDecorator,
  helperTextDecorator,
  currentValue,
  options,
  itemToStringLabel,
  slotProps,
}: SingleInputProps<ItemValue>) {
  return (
    <BaseCombobox.Input
      id={id}
      render={(
        inputProps: HTMLProps<HTMLInputElement>,
        inputState: BaseCombobox.Input.State
      ) => {
        if (slotProps?.input?.render) {
          const customRender = slotProps.input.render(inputProps, inputState)
          if (customRender) {
            return customRender as React.ReactElement
          }
        }

        const hasValue = Boolean(currentValue)
        const hasSearchInput = Boolean(inputProps.value)
        const showClear = hasValue || hasSearchInput

        // Determine the input value to display
        let inputValue = inputProps.value || ""

        if (currentValue && !inputProps.value) {
          // Show the selected item's label when not searching
          if (itemToStringLabel) {
            inputValue = itemToStringLabel(currentValue as string)
          } else {
            const selectedOption = options?.find(
              (option) => option.id === currentValue
            )
            inputValue = selectedOption?.label || (currentValue as string)
          }
        }

        return (
          <Input
            {...inputProps}
            value={inputValue}
            label={label}
            required={required}
            helperText={helperText}
            error={error}
            disabled={disabled || inputState.disabled}
            placeholder={placeholder}
            fullWidth={fullWidth}
            size={size}
            labelDecorator={labelDecorator}
            helperTextDecorator={helperTextDecorator}
            className={classNames(
              "ApolloCombobox-input",
              slotProps?.input?.className
            )}
            endDecorator={
              <div
                className={classNames(
                  "ApolloCombobox-endDecorator",
                  styles.comboboxInputEndDecorator
                )}
              >
                {showClear && (
                  <BaseCombobox.Clear
                    className={styles.comboboxClear}
                    aria-label="Clear selection"
                    disabled={disabled}
                  >
                    <CloseIcon />
                  </BaseCombobox.Clear>
                )}
                <BaseCombobox.Trigger
                  className={styles.comboboxTrigger}
                  aria-label="Open popup"
                  disabled={disabled}
                >
                  <ChevronIcon />
                </BaseCombobox.Trigger>
              </div>
            }
          />
        )
      }}
    />
  )
}

