@layer legacy {
  .comboboxMenuPositioner {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    outline: 0;
    z-index: 701;
  }


  .comboboxMenuRoot {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    border-radius: 8px;
    background: var(--apl-colors-surface-static-ui-default, #FFF);

    width: var(--anchor-width);
    outline: none;

    &>* {
      flex-shrink: 0;
    }

    & :global(.ApolloMenuItem-root) {

      &:first-of-type,
      &:last-of-type {
        border-radius: 8px;
      }
    }
  }
}

@layer apollo {
  .comboboxMenuPositioner {
    outline: 0;
    box-shadow: 4px 4px 15px 3px rgba(0, 0, 0, 0.05);
    -webkit-box-shadow: 4px 4px 15px 3px rgba(0, 0, 0, 0.05);
    z-index: 701;
  }


  .comboboxMenuRoot {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    border-radius: var(--apl-alias-radius-radius4, 8px);
    background: var(--apl-alias-color-background-and-surface-background, #FFF);

    width: var(--anchor-width);
    outline: none;

    &>* {
      flex-shrink: 0;
    }

    & :global(.ApolloMenuItem-root) {

      &:first-of-type,
      &:last-of-type {
        border-radius: var(--apl-alias-radius-radius4, 8px);
      }
    }
  }
}

.comboboxRoot {
  outline: none;
  width: fit-content;
  position: relative;
}

.comboboxFieldFullWidth {
  width: 100%;
}

.comboboxMenuItem {
  width: 100%;
  outline: none;
}



.Popup {
  box-sizing: border-box;
  border-radius: 0.5rem;
  padding-block: 0.5rem;
  background-color: canvas;
  color: var(--color-gray-900);
  width: var(--anchor-width);
  max-width: var(--available-width);
  max-height: min(var(--available-height), 24rem);
  overflow-y: auto;
  scroll-padding-block: 0.5rem;
  overscroll-behavior: contain;
  transition:
    opacity 0.1s,
    transform 0.1s;
  transform-origin: var(--transform-origin);

  &[data-starting-style],
  &[data-ending-style] {
    opacity: 0;
    transform: scale(0.95);
  }

  @media (prefers-color-scheme: light) {
    outline: 1px solid var(--color-gray-200);
    box-shadow:
      0 10px 15px -3px var(--color-gray-200),
      0 4px 6px -4px var(--color-gray-200);
  }

  @media (prefers-color-scheme: dark) {
    outline: 1px solid var(--color-gray-300);
    outline-offset: -1px;
  }
}