import { cva } from "class-variance-authority"
import classNames from "classnames"

import { IconButton } from "../icon-button"
import { Typography } from "../typography"
import styles from "./chip.module.css"
import type { ChipProps } from "./ChipProps"
import { CloseIcon } from "../common"

const ChipVariants = cva(styles.chipRoot, {
  variants: {
    variant: {
      filled: styles.chipFilled,
      outline: styles.chipOutline,
    },
    size: {
      large: styles.chipLarge,
      medium: styles.chipMedium,
      small: styles.chipSmall,
    },
    color: {
      primary: styles.chipPrimary,
      negative: styles.chipNegative,
    },
  },
  defaultVariants: {
    variant: "filled",
    size: "medium",
    color: "primary",
  },
})

export function Chip({
  label,
  ref,
  onClose,
  onCheck,
  disabled,
  variant = "filled",
  size = "medium",
  color = "primary",
  className,
  ...divProps
}: ChipProps) {
  const ChipVariantStyles = ChipVariants({
    variant,
    size,
    color,
  })

  const handleCheck = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation()
    onCheck?.(event)
  }

  const handleClose = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation()
    onClose?.(event)
  }

  return (
    <div
      {...divProps}
      ref={ref}
      className={classNames(
        "ApolloChip-root",
        styles.chip,
        ChipVariantStyles,
        { [styles.chipDisabled]: disabled },
        className
      )}
    >
      {onCheck && (
        <IconButton
          className="ApolloChip-checkIcon"
          size={'small'}
          onClick={handleCheck}
          disabled={disabled}
          color={color}
        >
          <CheckIcon />
        </IconButton>
      )}
      <Typography level={size === "small" ? "bodySmall" : "bodyMedium"} color={color} className="ApolloChip-label">
        {label}
      </Typography>
      {onClose && (
        <IconButton
          className="ApolloChip-closeIcon"
          size={'small'}
          onClick={handleClose}
          disabled={disabled}
          color={color}
        >
          <CloseIcon />
        </IconButton>
      )}
    </div>
  )
}

function CheckIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="15"
      height="14"
      viewBox="0 0 15 14"
      fill="none"
    >
      <path
        d="M13.3853 2.33333H12.386C12.2459 2.33333 12.1129 2.39857 12.0271 2.51019L6.13254 10.0815L3.30606 6.45023C3.2633 6.39519 3.2088 6.35068 3.14665 6.32005C3.0845 6.28942 3.01631 6.27347 2.94721 6.27338H1.94786C1.85207 6.27338 1.79917 6.385 1.85779 6.46038L5.77369 11.4905C5.95669 11.7254 6.30839 11.7254 6.49282 11.4905L13.4754 2.51888C13.534 2.44495 13.4811 2.33333 13.3853 2.33333Z"
        fill="currentColor"
      />
    </svg>
  )
}
